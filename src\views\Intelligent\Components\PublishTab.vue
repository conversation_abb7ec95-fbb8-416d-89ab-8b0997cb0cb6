<script setup lang="ts">
import { ref, reactive } from 'vue';
import { message } from 'ant-design-vue';
import { PlusOutlined, MinusCircleOutlined } from '@ant-design/icons-vue';
import type { ColumnType } from 'ant-design-vue/es/table';

// 定义 props
interface Props {
  agentId: string;
}
const props = defineProps<Props>();

// 发布渠道数据接口
interface PublishChannel {
  key: string;
  channel: string;
  channelIcon: string;
  description: string;
  status: 'published' | 'unpublished';
  publishTime?: string;
  actions: string[];
}

// 表格数据
const dataSource = ref<PublishChannel[]>([
  {
    key: '1',
    channel: '网页版',
    channelIcon: 'globe',
    description: '可通过 PC 端浏览器访问的网页版本对话',
    status: 'published',
    publishTime: '2025.07.30 11:36:24',
    actions: ['visit', 'copy']
  },
  {
    key: '2',
    channel: '网站嵌入',
    channelIcon: 'code',
    description: '通过 iframe/JS 嵌入到其他网站或应用的嵌入式体验版本',
    status: 'unpublished',
    actions: ['website']
  }
]);

// 表格列配置
const columns: ColumnType[] = [
  {
    title: '发布渠道',
    dataIndex: 'channel',
    key: 'channel',
    width: 200,
  },
  {
    title: '发布结果',
    dataIndex: 'status',
    key: 'status',
    width: 200,
  },
  {
    title: '操作',
    key: 'actions',
    width: 300,
  },
];

// 弹窗状态
const isModalVisible = ref(false);

// 表单数据
interface FormData {
  websiteName: string;
  domains: string[];
}

const formData = reactive<FormData>({
  websiteName: '',
  domains: []
});

// 表单验证规则
const rules = {
  websiteName: [
    { required: true, message: '请输入网站名称', trigger: 'blur' },
    { max: 30, message: '网站名称不能超过 30 个字符', trigger: 'blur' }
  ]
};

// 操作处理函数
const handleVisit = () => {
  message.info('正在跳转到网页版...');
};

const handleCopyLink = () => {
  // 这里应该复制实际的链接
  navigator.clipboard.writeText('https://example.com/agent/' + props.agentId);
  message.success('链接已复制到剪贴板');
};

const handleGoToWebsite = () => {
  isModalVisible.value = true;
};

// 弹窗相关函数
const handleModalOk = () => {
  // 这里应该提交表单数据
  console.log('提交表单数据：', formData);
  message.success('网站添加成功');
  isModalVisible.value = false;
  resetForm();
};

const handleModalCancel = () => {
  isModalVisible.value = false;
  resetForm();
};

const resetForm = () => {
  formData.websiteName = '';
  formData.domains = [];
};

// 域名输入框相关函数
const addDomain = () => {
  if (formData.domains.length < 10) {
    formData.domains.push('');
  }
};

const removeDomain = (index: number) => {
  formData.domains.splice(index, 1);
};
</script>

<template>
  <div class="publish-tab-container">
    <div class="publish-header">
      <h3 class="publish-title">发布渠道及结果如下</h3>
    </div>

    <div class="publish-table-wrapper">
      <a-table
        :data-source="dataSource"
        :columns="columns"
        :pagination="false"
        :bordered="true"
        size="middle"
      >
        <!-- 发布渠道列 -->
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'channel'">
            <div class="channel-cell">
              <div class="channel-icon">
                <img
                  v-if="record.channelIcon === 'globe'"
                  src="@/assets/image/base/pictures/web-icon.png"
                  alt="网页版"
                  class="channel-icon-img"
                />
                <img
                  v-else-if="record.channelIcon === 'code'"
                  src="@/assets/image/base/pictures/web-embedding.png"
                  alt="网站嵌入"
                  class="channel-icon-img"
                />
              </div>
              <div class="channel-info">
                <div class="channel-name">{{ record.channel }}</div>
                <div class="channel-desc">{{ record.description }}</div>
              </div>
            </div>
          </template>

          <!-- 发布结果列 -->
          <template v-else-if="column.dataIndex === 'status'">
            <div class="status-cell">
              <div v-if="record.status === 'published'" class="status-published">
                <div class="status-row">
                  <a-tag color="success">已发布</a-tag>
                </div>
                <div class="publish-time">{{ record.publishTime }}</div>
              </div>
              <div v-else class="status-unpublished">
                <a-tag color="warning">未发布</a-tag>
              </div>
            </div>
          </template>

          <!-- 操作列 -->
          <template v-else-if="column.key === 'actions'">
            <div class="actions-cell">
              <template v-if="record.status === 'published'">
                <a-button
                type="link"
                  size="small"
                  class="action-btn"
                  @click="handleVisit"
                >
                  立即访问
                </a-button>
                <a-button
                  type="link"
                  size="small"
                  class="action-btn"
                  @click="handleCopyLink"
                >
                  复制智能体链接
                </a-button>
              </template>
              <template v-else>
                <a-button
                  type="link"
                  size="small"
                  class="action-btn"
                  @click="handleGoToWebsite"
                >
                  新增网站
                </a-button>
              </template>
            </div>
          </template>
        </template>
      </a-table>
    </div>

    <!-- 新增网站弹窗 -->
    <a-modal
      v-model:open="isModalVisible"
      title="新增网站"
      :width="600"
      ok-text="完成并生成代码"
      cancel-text="取消"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
    >
      <div class="modal-content">
        <!-- 网站名称 -->
        <div class="form-item">
          <label class="form-label">网站名称</label>
          <a-input
            v-model:value="formData.websiteName"
            placeholder="请输入网站名称"
            :maxlength="30"
            :show-count="true"
            class="form-input"
          />
        </div>

        <!-- 网站域名 -->
        <div class="form-item">
          <label class="form-label">网站域名</label>
          <div class="domain-description">
            配置需要调用智能体的域名。为提升资源安全性，请按实际使用情况填写正确的域名。如不配置域名，则不限制请求来源
          </div>

          <!-- 域名输入框列表 -->
          <div class="domain-inputs">
            <div
              v-for="(domain, index) in formData.domains"
              :key="index"
              class="domain-input-row"
            >
              <a-input
                v-model:value="formData.domains[index]"
                placeholder="请填写需要接入的网站域名，如：https://hqshuke.com/"
                class="domain-input"
              />
              <MinusCircleOutlined
                class="remove-domain-btn"
                @click="removeDomain(index)"
              />
            </div>
          </div>

          <!-- 新增域名按钮 -->
          <div class="add-domain-btn-wrapper">
            <a-button
              v-if="formData.domains.length < 10"
              type="dashed"
              class="add-domain-btn"
              @click="addDomain"
            >
              <PlusOutlined />
              新增域名
            </a-button>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<style scoped>
.publish-tab-container {
  padding: 20px;
  height: 100%;
  background: #fff;
}

.publish-header {
  margin-bottom: 20px;
}

.publish-title {
  font-size: 16px;
  font-weight: 500;
  color: #262626;
  margin: 0;
}

.publish-table-wrapper {
  background: #fff;
}

/* 发布渠道列样式 */
.channel-cell {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.channel-icon {
  font-size: 18px;
  line-height: 1;
  margin-top: 2px;
  color: #1890ff;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.channel-info {
  flex: 1;
}

.channel-name {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
}

.channel-desc {
  font-size: 12px;
  color: #8c8c8c;
  line-height: 1.4;
}

/* 发布结果列样式 */
.status-cell {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.status-published {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.status-row {
  display: flex;
  align-items: center;
}

.status-unpublished {
  display: flex;
  align-items: center;
}

.publish-time {
  font-size: 12px;
  color: #8c8c8c;
  margin-top: 2px;
}

/* 操作列样式 */
.actions-cell {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: flex-start;
}

.action-btn {
  padding: 0;
  height: auto;
  line-height: 1.4;
}

.action-btn + .action-btn {
  margin-top: 8px;
}

/* 表格样式调整 */
:deep(.ant-table) {
  border-radius: 6px;
}

:deep(.ant-table-thead > tr > th) {
  background: #fafafa;
  font-weight: 500;
  color: #262626;
}

:deep(.ant-table-tbody > tr > td) {
  padding: 16px;
  vertical-align: top;
}

:deep(.ant-table-tbody > tr:hover > td) {
  background: #f5f5f5;
}

/* 弹窗样式 */
.modal-content {
  padding: 20px 0;
}

.form-item {
  margin-bottom: 24px;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #262626;
  margin-bottom: 8px;
}

.form-input {
  width: 100%;
}

.domain-description {
  font-size: 12px;
  color: #8c8c8c;
  line-height: 1.5;
  margin-bottom: 16px;
  padding: 12px;
  background: #f5f5f5;
  border-radius: 4px;
}

.domain-inputs {
  margin-bottom: 16px;
}

.domain-input-row {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.domain-input {
  flex: 1;
}

.remove-domain-btn {
  font-size: 16px;
  color: #ff4d4f;
  cursor: pointer;
  padding: 4px;
  transition: color 0.3s;
}

.remove-domain-btn:hover {
  color: #ff7875;
}

.add-domain-btn-wrapper {
  display: flex;
  justify-content: flex-start;
}

.add-domain-btn {
  height: 40px;
  border: 1px dashed #d9d9d9;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 0 16px;
}

.add-domain-btn:hover {
  border-color: #1890ff;
  color: #1890ff;
}
</style>
